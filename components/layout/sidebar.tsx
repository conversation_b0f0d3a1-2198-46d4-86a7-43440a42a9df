"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Home, 
  CreditCard, 
  TrendingUp, 
  PieChart, 
  Target, 
  Calendar, 
  MessageSquare,
  Settings,
  ChevronLeft,
  ChevronRight,
  Wallet,
  Receipt,
  Upload
} from 'lucide-react';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { id: 'transactions', label: 'Transactions', icon: CreditCard },
  { id: 'analytics', label: 'Analytics', icon: PieChart },
  { id: 'investments', label: 'Investments', icon: TrendingUp },
  { id: 'budgets', label: 'Budgets', icon: Target },
  { id: 'subscriptions', label: 'Subscriptions', icon: Calendar },
  { id: 'receipts', label: 'Receipts', icon: Receipt },
  { id: 'upload', label: 'Upload Files', icon: Upload },
  { id: 'ai-chat', label: 'AI Assistant', icon: MessageSquare },
];

export function Sidebar({ activeSection, onSectionChange, isMobileOpen = false, onMobileClose }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  console.log("Sidebar rendered with active section:", activeSection);

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={onMobileClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:relative inset-y-0 left-0 z-50 lg:z-auto
        bg-card/80 backdrop-blur-xl border-r border-border/50
        transition-all duration-300 shadow-strong lg:shadow-soft
        ${isCollapsed ? 'w-16' : 'w-64'}
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 lg:p-6">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                <Wallet className="h-5 w-5 text-white" />
              </div>
              <div>
                <span className="text-lg font-bold bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                  FinanceHub
                </span>
                <p className="text-xs text-muted-foreground">Smart Finance</p>
              </div>
            </div>
          )}

          {/* Desktop collapse button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="ml-auto hover:bg-muted/80 rounded-lg hidden lg:flex"
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>

          {/* Mobile close button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onMobileClose}
            className="ml-auto hover:bg-muted/80 rounded-lg lg:hidden"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        <Separator />

        {/* Navigation */}
        <ScrollArea className="flex-1 p-2 lg:p-3">
          <nav className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;

              return (
                <Button
                  key={item.id}
                  variant={isActive ? "default" : "ghost"}
                  className={`group w-full justify-start transition-all duration-300 relative overflow-hidden ${isCollapsed ? 'px-2 py-3' : 'px-4 py-3'} ${
                    isActive
                      ? 'bg-gradient-to-r from-primary to-primary-dark text-white shadow-lg shadow-primary/25 scale-[1.02] rounded-xl border-0'
                      : 'hover:bg-muted/80 hover:scale-[1.01] rounded-lg text-muted-foreground hover:text-foreground border-0'
                  }`}
                  onClick={() => {
                    console.log("Navigation clicked:", item.id);
                    onSectionChange(item.id);
                    onMobileClose?.(); // Close mobile menu on selection
                  }}
                >
                  <Icon className={`h-5 w-5 transition-transform duration-300 ${isCollapsed ? '' : 'mr-3'} ${
                    isActive ? 'scale-110' : 'group-hover:scale-105'
                  }`} />
                  {!isCollapsed && <span className="font-medium text-sm">{item.label}</span>}
                  {isActive && !isCollapsed && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer" />
                  )}
                </Button>
              );
            })}
          </nav>
        </ScrollArea>

        {/* Settings */}
        <div className="p-2 lg:p-3">
          <Separator className="mb-3" />
          <Button
            variant="ghost"
            className={`group w-full justify-start transition-all duration-300 hover:bg-muted/80 hover:scale-[1.01] rounded-lg ${isCollapsed ? 'px-2 py-3' : 'px-4 py-3'}`}
            onClick={() => {
              onSectionChange('settings');
              onMobileClose?.();
            }}
          >
            <Settings className={`h-5 w-5 transition-transform duration-300 group-hover:scale-105 ${isCollapsed ? '' : 'mr-3'}`} />
            {!isCollapsed && <span className="font-medium text-sm">Settings</span>}
          </Button>
        </div>
      </div>

      {/* Mobile Menu Trigger - Add this to Header component */}
      </>
  );
}