"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import {
  ArrowUp,
  ArrowDown,
  DollarSign,
  CreditCard,
  TrendingUp,
  Target,
  Plus,
  Eye,
  EyeOff,
} from "lucide-react";
import { useState } from "react";
import { SpendingChart } from "./spending-chart";
import { CategoryChart } from "./category-chart";

interface DashboardProps {
  onSectionChange: (section: string) => void;
}

export function Dashboard({ onSectionChange }: DashboardProps) {
  const [showBalance, setShowBalance] = useState(true);

  const stats = [
    {
      title: "Total Balance",
      value: showBalance ? "$24,536.00" : "••••••",
      change: "+12.5%",
      changeType: "positive" as const,
      icon: DollarSign,
      description: "Across all accounts",
    },
    {
      title: "Monthly Spending",
      value: showBalance ? "$3,248.00" : "••••••",
      change: "-8.2%",
      changeType: "negative" as const,
      icon: CreditCard,
      description: "This month",
    },
    {
      title: "Investments",
      value: showBalance ? "$18,940.00" : "••••••",
      change: "+24.8%",
      changeType: "positive" as const,
      icon: TrendingUp,
      description: "Portfolio value",
    },
    {
      title: "Savings Goal",
      value: showBalance ? "$8,760.00" : "••••••",
      change: "87%",
      changeType: "neutral" as const,
      icon: Target,
      description: "Emergency fund",
    },
  ];

  const recentTransactions = [
    {
      id: 1,
      description: "Starbucks Coffee",
      amount: -5.95,
      category: "Food & Dining",
      date: "Today",
    },
    {
      id: 2,
      description: "Monthly Salary",
      amount: 5000.0,
      category: "Income",
      date: "Yesterday",
    },
    {
      id: 3,
      description: "Netflix Subscription",
      amount: -15.99,
      category: "Entertainment",
      date: "2 days ago",
    },
    {
      id: 4,
      description: "Grocery Shopping",
      amount: -127.45,
      category: "Food & Dining",
      date: "3 days ago",
    },
    {
      id: 5,
      description: "Gym Membership",
      amount: -49.99,
      category: "Health & Fitness",
      date: "3 days ago",
    },
  ];

  const budgets = [
    { category: "Food & Dining", spent: 420, limit: 600, percentage: 70 },
    { category: "Transportation", spent: 180, limit: 300, percentage: 60 },
    { category: "Entertainment", spent: 95, limit: 200, percentage: 48 },
    { category: "Shopping", spent: 340, limit: 400, percentage: 85 },
  ];

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Stats Cards */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card
              key={index}
              className="card-premium group hover:shadow-strong transition-all duration-300 hover:scale-[1.02]"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className="flex items-center space-x-2">
                  {stat.title === "Total Balance" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowBalance(!showBalance)}
                      className="hover:bg-muted/80 rounded-lg"
                    >
                      {showBalance ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                  <div
                    className={`p-2 rounded-lg ${
                      index === 0
                        ? "bg-gradient-to-br from-primary/20 to-primary/10"
                        : index === 1
                        ? "bg-gradient-to-br from-success/20 to-success/10"
                        : index === 2
                        ? "bg-gradient-to-br from-warning/20 to-warning/10"
                        : "bg-gradient-to-br from-info/20 to-info/10"
                    }`}
                  >
                    <Icon
                      className={`h-4 w-4 group-hover:scale-110 transition-transform duration-300 ${
                        index === 0
                          ? "text-primary"
                          : index === 1
                          ? "text-success"
                          : index === 2
                          ? "text-warning"
                          : "text-info"
                      }`}
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div
                  className={`text-2xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${
                    index === 0
                      ? "from-primary to-secondary"
                      : index === 1
                      ? "from-success to-secondary"
                      : index === 2
                      ? "from-warning to-destructive"
                      : "from-info to-accent"
                  }`}
                >
                  {stat.value}
                </div>
                <div className="flex items-center space-x-2 text-xs">
                  <span className="text-muted-foreground">
                    {stat.description}
                  </span>
                  <Badge
                    variant={
                      stat.changeType === "positive"
                        ? "default"
                        : stat.changeType === "negative"
                        ? "destructive"
                        : "secondary"
                    }
                    className={`ml-auto transition-all duration-300 ${
                      stat.changeType === "positive"
                        ? "bg-success/20 text-success hover:bg-success/30"
                        : stat.changeType === "negative"
                        ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {stat.changeType === "positive" && (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    )}
                    {stat.changeType === "negative" && (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {stat.change}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts Row */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
        <Card className="card-premium">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">
              Spending Trends
            </CardTitle>
            <CardDescription>
              Your spending over the last 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SpendingChart />
          </CardContent>
        </Card>

        <Card className="card-premium">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">
              Category Breakdown
            </CardTitle>
            <CardDescription>
              Current month spending by category
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategoryChart />
          </CardContent>
        </Card>
      </div>

      {/* Bottom Row */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Recent Transactions */}
        <Card className="card-premium">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg sm:text-xl">
                Recent Transactions
              </CardTitle>
              <CardDescription>Your latest financial activity</CardDescription>
            </div>
            <Button
              size="sm"
              onClick={() => onSectionChange("transactions")}
              className="btn-outline-modern"
            >
              View All
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between"
              >
                <div className="flex flex-col">
                  <span className="font-medium text-sm">
                    {transaction.description}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {transaction.category} • {transaction.date}
                  </span>
                </div>
                <span
                  className={`font-semibold ${
                    transaction.amount > 0 ? "text-success" : "text-destructive"
                  }`}
                >
                  {transaction.amount > 0 ? "+" : ""}$
                  {Math.abs(transaction.amount).toFixed(2)}
                </span>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Budget Overview */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Budget Overview</CardTitle>
              <CardDescription>How you're tracking this month</CardDescription>
            </div>
            <Button size="sm" onClick={() => onSectionChange("budgets")}>
              <Plus className="h-4 w-4 mr-2" />
              New Budget
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {budgets.map((budget, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">{budget.category}</span>
                  <span className="text-muted-foreground">
                    ${budget.spent} / ${budget.limit}
                  </span>
                </div>
                <Progress
                  value={budget.percentage}
                  className={`h-2 ${
                    budget.percentage > 80 ? "bg-red-100" : "bg-gray-100"
                  }`}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{budget.percentage}% used</span>
                  <span>${budget.limit - budget.spent} remaining</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
