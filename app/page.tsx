"use client";

import { useState } from 'react';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/sonner';
import { Header } from '@/components/layout/header';
import { Sidebar } from '@/components/layout/sidebar';
import { Dashboard } from '@/components/dashboard/dashboard';
import { TransactionTable } from '@/components/transactions/transaction-table';
import { TransactionForm } from '@/components/transactions/transaction-form';
import { FileUpload } from '@/components/file-upload/file-upload';
import { AiChat } from '@/components/ai-chat/ai-chat';
import { InvestmentPortfolio } from '@/components/investments/investment-portfolio';
import { SubscriptionManagement } from '@/components/subscriptions/subscription-management';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Settings, Moon, Sun, Database, Shield, Zap } from 'lucide-react';

export default function FinanceApp() {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState(null);

  console.log("Main FinanceApp rendered with active section:", activeSection);

  const handleSectionChange = (section: string) => {
    console.log("Section changed to:", section);
    setActiveSection(section);
  };

  const handleAddTransaction = () => {
    console.log("Add transaction requested");
    setEditingTransaction(null);
    setShowTransactionForm(true);
  };

  const handleEditTransaction = (transaction: any) => {
    console.log("Edit transaction requested:", transaction);
    setEditingTransaction(transaction);
    setShowTransactionForm(true);
  };

  const handleTransactionSubmit = (transaction: any) => {
    console.log("Transaction submitted:", transaction);
    // In a real app, this would make an API call to FastAPI backend
    // Example: await fetch('/api/transactions', { method: 'POST', body: JSON.stringify(transaction) })
  };

  const handleBulkEdit = (selectedIds: string[]) => {
    console.log("Bulk edit requested for:", selectedIds);
    // Handle bulk editing logic here
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard onSectionChange={handleSectionChange} />;
      
      case 'transactions':
        return (
          <TransactionTable
            onAddTransaction={handleAddTransaction}
            onEditTransaction={handleEditTransaction}
            onBulkEdit={handleBulkEdit}
          />
        );
      
      case 'analytics':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Analytics</CardTitle>
                <CardDescription>Deep insights into your financial patterns</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Spending Velocity</h4>
                    <p className="text-2xl font-bold text-primary">$127/day</p>
                    <p className="text-sm text-muted-foreground">Average daily spending</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Cash Flow Ratio</h4>
                    <p className="text-2xl font-bold text-green-600">1.54</p>
                    <p className="text-sm text-muted-foreground">Income to expense ratio</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Savings Rate</h4>
                    <p className="text-2xl font-bold text-blue-600">18%</p>
                    <p className="text-sm text-muted-foreground">Of total income</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'investments':
        return <InvestmentPortfolio />;
      
      case 'budgets':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Budget & Goal Tracking</CardTitle>
                <CardDescription>Monitor your financial progress and goals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-2">Budget Management Coming Soon</h3>
                  <p className="text-muted-foreground mb-4">
                    Advanced budgeting tools with AI-powered insights and recommendations
                  </p>
                  <Badge variant="secondary">Under Development</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'subscriptions':
        return <SubscriptionManagement />;
      
      case 'receipts':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Receipt Management</CardTitle>
                <CardDescription>Organize and categorize your receipts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-lg font-semibold mb-2">Smart Receipt Processing</h3>
                  <p className="text-muted-foreground mb-4">
                    AI-powered receipt scanning and automatic expense categorization
                  </p>
                  <Badge variant="secondary">Coming Soon</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'upload':
        return <FileUpload />;
      
      case 'ai-chat':
        return <AiChat />;
      
      case 'settings':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
                <CardDescription>Customize your finance app experience</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-3">
                        <Database className="h-5 w-5 text-primary" />
                        <h4 className="font-medium">Data Management</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        Export your data, set up backups, and manage data retention
                      </p>
                      <Button variant="outline" size="sm">Manage Data</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-3">
                        <Shield className="h-5 w-5 text-primary" />
                        <h4 className="font-medium">Security</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        Two-factor authentication, password settings, and privacy controls
                      </p>
                      <Button variant="outline" size="sm">Security Settings</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-3">
                        <Zap className="h-5 w-5 text-primary" />
                        <h4 className="font-medium">Integrations</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        Connect bank accounts, credit cards, and investment platforms
                      </p>
                      <Button variant="outline" size="sm">Manage Connections</Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-3">
                        <Settings className="h-5 w-5 text-primary" />
                        <h4 className="font-medium">Preferences</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        Currency, date format, notifications, and display preferences
                      </p>
                      <Button variant="outline" size="sm">Edit Preferences</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      
      default:
        return <Dashboard onSectionChange={handleSectionChange} />;
    }
  };

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex h-screen bg-gradient-to-br from-background via-background to-muted/20 relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0)_50%)] dark:bg-[radial-gradient(circle_at_50%_120%,rgba(99,102,241,0.1),rgba(255,255,255,0)_50%)]" />
        
        {/* Sidebar */}
        <Sidebar activeSection={activeSection} onSectionChange={handleSectionChange} />
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden relative">
          {/* Header */}
          <Header activeSection={activeSection} />
          
          {/* Content Area */}
          <main className="flex-1 overflow-y-auto p-6 relative">
            <div className="animate-fade-in">
              {renderContent()}
            </div>
          </main>
        </div>

        {/* Transaction Form Modal */}
        <TransactionForm
          isOpen={showTransactionForm}
          onClose={() => setShowTransactionForm(false)}
          onSubmit={handleTransactionSubmit}
          initialData={editingTransaction}
        />

        {/* Toast Notifications */}
        <Toaster />
      </div>
    </ThemeProvider>
  );
}