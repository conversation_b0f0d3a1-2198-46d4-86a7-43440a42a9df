import "./globals.css";
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Cool Macaly App",
  description: "Generated by <PERSON><PERSON>",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
        <body className={inter.className}>{children}</body>
    </html>
  );
}