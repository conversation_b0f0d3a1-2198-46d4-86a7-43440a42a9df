@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Light Theme - Inspired by Linear, Notion, and Stripe */
    --background: 252 252 253; /* #FCFCFD - Ultra clean white */
    --foreground: 24 24 27; /* #18181B - Deep charcoal */
    --card: 255 255 255;
    --card-foreground: 24 24 27;
    --popover: 255 255 255;
    --popover-foreground: 24 24 27;
    --primary: 99 102 241; /* #6366F1 - Modern indigo */
    --primary-foreground: 255 255 255;
    --secondary: 16 185 129; /* #10B981 - Vibrant emerald */
    --secondary-foreground: 255 255 255;
    --muted: 248 250 252; /* #F8FAFC - Subtle gray */
    --muted-foreground: 100 116 139; /* #64748B - Medium gray */
    --accent: 139 92 246; /* #8B5CF6 - Purple accent */
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68; /* #EF4444 - Modern red */
    --destructive-foreground: 255 255 255;
    --border: 226 232 240; /* #E2E8F0 - Soft border */
    --input: 241 245 249; /* #F1F5F9 - Input background */
    --ring: 99 102 241; /* Primary focus ring */
    
    /* Enhanced Chart Colors - More vibrant and accessible */
    --chart-1: 99 102 241; /* Indigo */
    --chart-2: 16 185 129; /* Emerald */
    --chart-3: 239 68 68; /* Red */
    --chart-4: 245 158 11; /* Amber */
    --chart-5: 139 92 246; /* Purple */
    --chart-6: 236 72 153; /* Pink */
    --chart-7: 14 165 233; /* Sky blue */
    --chart-8: 34 197 94; /* Green */
    
    /* Success and warning colors */
    --success: 34 197 94; /* #22C55E - Success green */
    --success-foreground: 255 255 255;
    --warning: 251 146 60; /* #FB923C - Warning orange */
    --warning-foreground: 255 255 255;
    --info: 59 130 246; /* #3B82F6 - Info blue */
    --info-foreground: 255 255 255;
    
    --radius: 0.75rem;
  }
  
  .dark {
    /* Dark Theme - Modern and sophisticated */
    --background: 9 9 11; /* #09090B - Rich black */
    --foreground: 250 250 250; /* #FAFAFA - Clean white */
    --card: 24 24 27; /* #18181B - Dark card */
    --card-foreground: 250 250 250;
    --popover: 24 24 27;
    --popover-foreground: 250 250 250;
    --primary: 129 140 248; /* #818CF8 - Bright indigo */
    --primary-foreground: 24 24 27;
    --secondary: 52 211 153; /* #34D399 - Bright emerald */
    --secondary-foreground: 24 24 27;
    --muted: 39 39 42; /* #27272A - Dark muted */
    --muted-foreground: 161 161 170; /* #A1A1AA - Light gray */
    --accent: 167 139 250; /* #A78BFA - Light purple */
    --accent-foreground: 24 24 27;
    --destructive: 248 113 113; /* #F87171 - Bright red */
    --destructive-foreground: 24 24 27;
    --border: 39 39 42; /* #27272A - Dark border */
    --input: 39 39 42; /* #27272A - Dark input */
    --ring: 129 140 248; /* Primary focus ring */
    
    /* Dark theme chart colors - Brighter and more vibrant */
    --chart-1: 129 140 248; /* Bright indigo */
    --chart-2: 52 211 153; /* Bright emerald */
    --chart-3: 248 113 113; /* Bright red */
    --chart-4: 251 191 36; /* Bright amber */
    --chart-5: 167 139 250; /* Bright purple */
    --chart-6: 244 114 182; /* Bright pink */
    --chart-7: 56 189 248; /* Bright sky */
    --chart-8: 74 222 128; /* Bright green */
    
    /* Dark theme status colors */
    --success: 74 222 128; /* #4ADE80 - Bright success */
    --success-foreground: 24 24 27;
    --warning: 251 146 60; /* #FB923C - Warning orange */
    --warning-foreground: 24 24 27;
    --info: 96 165 250; /* #60A5FA - Bright info */
    --info-foreground: 24 24 27;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
  }
}

/* Enhanced UI Components */
@layer components {
  /* Glassmorphism effect for cards */
  .glass-card {
    @apply backdrop-blur-xl bg-white/80 border border-white/20 shadow-xl shadow-black/5;
  }
  
  .dark .glass-card {
    @apply backdrop-blur-xl bg-black/40 border border-white/10 shadow-xl shadow-black/20;
  }
  
  /* Enhanced button hover effects */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary via-accent to-secondary;
    @apply hover:shadow-lg hover:shadow-primary/25 transition-all duration-300;
    @apply hover:scale-[1.02] active:scale-[0.98];
  }
  
  /* Animated gradient backgrounds */
  .gradient-animate {
    background: linear-gradient(270deg, hsl(var(--primary)), hsl(var(--accent)), hsl(var(--secondary)));
    background-size: 600% 600%;
    animation: gradient-shift 8s ease infinite;
  }
  
  /* Modern focus rings */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background;
  }
  
  /* Enhanced shadow system */
  .shadow-soft {
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.05), 0 4px 16px -4px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 16px -4px rgba(0, 0, 0, 0.08), 0 8px 32px -8px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-strong {
    box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.12), 0 16px 64px -16px rgba(0, 0, 0, 0.08);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Hide Next.js badge and error messages */
body > nextjs-portal {
  display: none;
}
