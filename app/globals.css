@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Ultra-Modern Light Theme - Finance Professional */
    --background: 250 251 252; /* #FAFBFC - Softer white with warmth */
    --foreground: 15 23 42; /* #0F172A - Rich navy for better readability */
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    /* Primary: Professional Blue-Indigo Gradient */
    --primary: 59 130 246; /* #3B82F6 - Professional blue */
    --primary-foreground: 255 255 255;
    --primary-light: 147 197 253; /* #93C5FD - Light blue */
    --primary-dark: 29 78 216; /* #1D4ED8 - Dark blue */

    /* Secondary: Success Green */
    --secondary: 34 197 94; /* #22C55E - Success green */
    --secondary-foreground: 255 255 255;
    --secondary-light: 134 239 172; /* #86EFAC - Light green */
    --secondary-dark: 21 128 61; /* #15803D - Dark green */

    /* Neutral Grays - More sophisticated */
    --muted: 248 250 252; /* #F8FAFC - Ultra light gray */
    --muted-foreground: 71 85 105; /* #475569 - Medium gray */
    --muted-dark: 226 232 240; /* #E2E8F0 - Border gray */

    /* Accent: Premium Purple */
    --accent: 124 58 237; /* #7C3AED - Rich purple */
    --accent-foreground: 255 255 255;
    --accent-light: 196 181 253; /* #C4B5FD - Light purple */

    /* Status Colors - Finance Optimized */
    --success: 34 197 94; /* #22C55E - Profit green */
    --success-foreground: 255 255 255;
    --warning: 245 158 11; /* #F59E0B - Caution amber */
    --warning-foreground: 255 255 255;
    --destructive: 239 68 68; /* #EF4444 - Loss red */
    --destructive-foreground: 255 255 255;
    --info: 14 165 233; /* #0EA5E9 - Info sky */
    --info-foreground: 255 255 255;

    /* Enhanced Chart Palette - Finance Data Visualization */
    --chart-1: 59 130 246; /* Primary blue */
    --chart-2: 34 197 94; /* Success green */
    --chart-3: 239 68 68; /* Danger red */
    --chart-4: 245 158 11; /* Warning amber */
    --chart-5: 124 58 237; /* Accent purple */
    --chart-6: 236 72 153; /* Pink */
    --chart-7: 14 165 233; /* Sky blue */
    --chart-8: 168 85 247; /* Violet */

    /* UI Elements */
    --border: 226 232 240; /* #E2E8F0 - Soft border */
    --input: 248 250 252; /* #F8FAFC - Input background */
    --ring: 59 130 246; /* Primary focus ring */

    /* Design System */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
  }

  .dark {
    /* Ultra-Modern Dark Theme - Premium Finance */
    --background: 3 7 18; /* #030712 - Deep navy black */
    --foreground: 248 250 252; /* #F8FAFC - Pure white */
    --card: 15 23 42; /* #0F172A - Rich navy card */
    --card-foreground: 248 250 252;
    --popover: 15 23 42;
    --popover-foreground: 248 250 252;

    /* Primary: Bright Professional Blue */
    --primary: 96 165 250; /* #60A5FA - Bright blue */
    --primary-foreground: 3 7 18;
    --primary-light: 147 197 253; /* #93C5FD - Light blue */
    --primary-dark: 37 99 235; /* #2563EB - Dark blue */

    /* Secondary: Vibrant Success Green */
    --secondary: 74 222 128; /* #4ADE80 - Bright green */
    --secondary-foreground: 3 7 18;
    --secondary-light: 134 239 172; /* #86EFAC - Light green */
    --secondary-dark: 34 197 94; /* #22C55E - Dark green */

    /* Neutral Grays - Dark Theme Optimized */
    --muted: 30 41 59; /* #1E293B - Dark muted */
    --muted-foreground: 148 163 184; /* #94A3B8 - Light gray */
    --muted-dark: 51 65 85; /* #334155 - Border gray */

    /* Accent: Vibrant Purple */
    --accent: 168 85 247; /* #A855F7 - Bright purple */
    --accent-foreground: 3 7 18;
    --accent-light: 196 181 253; /* #C4B5FD - Light purple */

    /* Status Colors - Dark Theme Finance */
    --success: 74 222 128; /* #4ADE80 - Bright profit green */
    --success-foreground: 3 7 18;
    --warning: 251 191 36; /* #FBBF24 - Bright caution amber */
    --warning-foreground: 3 7 18;
    --destructive: 248 113 113; /* #F87171 - Bright loss red */
    --destructive-foreground: 3 7 18;
    --info: 56 189 248; /* #38BDF8 - Bright info sky */
    --info-foreground: 3 7 18;

    /* Enhanced Chart Palette - Dark Theme */
    --chart-1: 96 165 250; /* Bright blue */
    --chart-2: 74 222 128; /* Bright green */
    --chart-3: 248 113 113; /* Bright red */
    --chart-4: 251 191 36; /* Bright amber */
    --chart-5: 168 85 247; /* Bright purple */
    --chart-6: 244 114 182; /* Bright pink */
    --chart-7: 56 189 248; /* Bright sky */
    --chart-8: 196 181 253; /* Light purple */

    /* UI Elements */
    --border: 51 65 85; /* #334155 - Dark border */
    --input: 30 41 59; /* #1E293B - Dark input */
    --ring: 96 165 250; /* Primary focus ring */

    /* Design System */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "cv11", "ss01", "cv02", "cv03", "cv04";
    font-variation-settings: "opsz" 32;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Enhanced Typography Scale */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }
  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }
  h5 {
    @apply text-lg md:text-xl lg:text-2xl;
  }
  h6 {
    @apply text-base md:text-lg lg:text-xl;
  }

  /* Improved focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary/50 ring-offset-2 ring-offset-background;
  }
}

/* Enhanced UI Components */
@layer components {
  /* Modern Glassmorphism Cards */
  .glass-card {
    @apply backdrop-blur-xl bg-white/90 border border-white/30 shadow-2xl shadow-black/5;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.85) 100%
    );
  }

  .dark .glass-card {
    @apply backdrop-blur-xl bg-slate-900/60 border border-white/10 shadow-2xl shadow-black/30;
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.8) 0%,
      rgba(30, 41, 59, 0.6) 100%
    );
  }

  /* Premium Card Styles */
  .card-premium {
    @apply bg-gradient-to-br from-card via-card to-muted/20 border border-border/50;
    @apply shadow-lg hover:shadow-xl transition-all duration-300;
    @apply hover:scale-[1.01] hover:border-primary/20;
  }

  .dark .card-premium {
    @apply bg-gradient-to-br from-card via-card to-muted/10;
    @apply hover:border-primary/30;
  }

  /* Enhanced Button Styles */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary via-primary to-primary-dark;
    @apply hover:shadow-lg hover:shadow-primary/25 transition-all duration-300;
    @apply hover:scale-[1.02] active:scale-[0.98];
    @apply border-0 text-white font-medium;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary via-secondary to-secondary-dark;
    @apply hover:shadow-lg hover:shadow-secondary/25 transition-all duration-300;
    @apply hover:scale-[1.02] active:scale-[0.98];
    @apply border-0 text-white font-medium;
  }

  .btn-outline-modern {
    @apply border-2 border-primary/20 bg-transparent text-primary;
    @apply hover:bg-primary hover:text-white hover:border-primary;
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary/20;
  }

  /* Animated Gradient Backgrounds */
  .gradient-animate {
    background: linear-gradient(
      270deg,
      hsl(var(--primary)),
      hsl(var(--accent)),
      hsl(var(--secondary))
    );
    background-size: 600% 600%;
    animation: gradient-shift 8s ease infinite;
  }

  .gradient-mesh {
    background: radial-gradient(
        at 40% 20%,
        hsl(var(--primary)) 0px,
        transparent 50%
      ),
      radial-gradient(at 80% 0%, hsl(var(--accent)) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsl(var(--secondary)) 0px, transparent 50%);
  }

  /* Modern Focus Rings */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Enhanced Shadow System */
  .shadow-soft {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .shadow-medium {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-strong {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .dark .shadow-glow {
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.4);
  }

  /* Modern Input Styles */
  .input-modern {
    @apply bg-input/50 border border-border/50 rounded-lg px-4 py-3;
    @apply focus:bg-background focus:border-primary/50 focus:ring-2 focus:ring-primary/20;
    @apply transition-all duration-200;
  }

  /* Responsive Grid Utilities */
  .grid-responsive {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-dashboard {
    @apply grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Mobile-first responsive utilities */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-text {
    @apply text-sm sm:text-base;
  }

  .mobile-hidden {
    @apply hidden sm:block;
  }

  .mobile-only {
    @apply block sm:hidden;
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Safe area handling for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Modern scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.6);
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: hsl(var(--muted) / 0.3);
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  /* Improve touch scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px;
  }
}

/* Hide Next.js badge and error messages */
body > nextjs-portal {
  display: none;
}
